#!/bin/bash

# Odoo URL/Endpoint Replacement Script (Bash Version)
# This script searches through Odoo addons for URLs/endpoints and replaces them with www.hotcodes.io
# Author: Custom Development Script

set -e

# Configuration
REPLACEMENT_URL="https://www.hotcodes.io"
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
DRY_RUN=false
REPORT_FILE="url_replacement_report_$(date +%Y%m%d_%H%M%S).txt"

# Odoo addon paths (adjust these based on your setup)
ADDON_PATHS=(
    "$(pwd)/odoo/addons"
    "/odoo/odoo-server/addons"
    "/odoo/custom/addons"
)

# URL patterns to replace (using sed-compatible regex)
declare -A URL_PATTERNS=(
    ["https://extract\.api\.odoo\.com"]="$REPLACEMENT_URL"
    ["https://iap-services\.odoo\.com"]="$REPLACEMENT_URL"
    ["https://nightly\.odoo\.com"]="$REPLACEMENT_URL"
    ["https://iap-scraper\.odoo\.com"]="$REPLACEMENT_URL"
    ["https://download\.odoo\.com"]="$REPLACEMENT_URL"
    ["https://iot-proxy\.odoo\.com"]="$REPLACEMENT_URL"
    ["https://l10n-be-codabox\.api\.odoo\.com"]="$REPLACEMENT_URL"
    ["https://website\.api\.odoo\.com"]="$REPLACEMENT_URL"
    ["https://olg\.api\.odoo\.com"]="$REPLACEMENT_URL"
    ["http://services\.odoo\.com"]="$REPLACEMENT_URL"
)

# File extensions to process
FILE_EXTENSIONS=("*.py" "*.xml" "*.js" "*.json" "*.yml" "*.yaml" "*.conf" "*.cfg")

# Counters
FILES_PROCESSED=0
REPLACEMENTS_MADE=0
FILES_MODIFIED=0

# Functions
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  --dry-run          Show what would be changed without making actual changes"
    echo "  --backup-dir DIR   Directory to store backups (default: backup_YYYYMMDD_HHMMSS)"
    echo "  --help             Show this help message"
    echo ""
    echo "This script replaces Odoo URLs with $REPLACEMENT_URL in addon files."
}

log_message() {
    local message="$1"
    echo "$message"
    echo "$message" >> "$REPORT_FILE"
}

create_backup() {
    local file_path="$1"
    if [ "$DRY_RUN" = false ]; then
        local backup_path="$BACKUP_DIR${file_path}"
        local backup_dir=$(dirname "$backup_path")
        mkdir -p "$backup_dir"
        cp "$file_path" "$backup_path"
        echo "Backup created: $backup_path"
    fi
}

process_file() {
    local file_path="$1"
    local changes_made=false
    local file_changes=0

    FILES_PROCESSED=$((FILES_PROCESSED + 1))

    # Check if file contains any of our target URLs
    local contains_target=false
    for pattern in "${!URL_PATTERNS[@]}"; do
        if grep -q "$pattern" "$file_path" 2>/dev/null; then
            contains_target=true
            break
        fi
    done

    if [ "$contains_target" = false ]; then
        return 0
    fi

    echo "Processing: $file_path"

    # Create backup before modification
    if [ "$DRY_RUN" = false ]; then
        create_backup "$file_path"
    fi

    # Apply replacements
    for pattern in "${!URL_PATTERNS[@]}"; do
        local replacement="${URL_PATTERNS[$pattern]}"

        # Count matches before replacement
        local matches=$(grep -c "$pattern" "$file_path" 2>/dev/null || echo "0")

        if [ "$matches" -gt 0 ]; then
            echo "  Found $matches occurrence(s) of: $pattern"

            if [ "$DRY_RUN" = false ]; then
                # Use sed to replace the pattern
                sed -i.tmp "s|$pattern|$replacement|g" "$file_path"
                rm -f "$file_path.tmp"
            fi

            file_changes=$((file_changes + matches))
            REPLACEMENTS_MADE=$((REPLACEMENTS_MADE + matches))
            changes_made=true

            log_message "  Replaced: $pattern -> $replacement ($matches times)"
        fi
    done

    if [ "$changes_made" = true ]; then
        FILES_MODIFIED=$((FILES_MODIFIED + 1))
        echo "  Total changes in file: $file_changes"
    fi
}

scan_directory() {
    local addon_path="$1"

    if [ ! -d "$addon_path" ]; then
        echo "Warning: Directory does not exist: $addon_path"
        return 0
    fi

    echo "Scanning directory: $addon_path"
    log_message "Scanning directory: $addon_path"

    # Find files with specified extensions
    for ext in "${FILE_EXTENSIONS[@]}"; do
        while IFS= read -r -d '' file; do
            # Skip backup directories and other excluded patterns
            if [[ "$file" == *"__pycache__"* ]] || \
               [[ "$file" == *".git"* ]] || \
               [[ "$file" == *"node_modules"* ]] || \
               [[ "$file" == *"backup_"* ]]; then
                continue
            fi

            process_file "$file"
        done < <(find "$addon_path" -name "$ext" -type f -print0 2>/dev/null)
    done
}

generate_report() {
    echo "================================================================================" > "$REPORT_FILE"
    echo "ODOO URL REPLACEMENT REPORT" >> "$REPORT_FILE"
    echo "================================================================================" >> "$REPORT_FILE"
    echo "Generated: $(date)" >> "$REPORT_FILE"
    echo "Mode: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "LIVE EXECUTION")" >> "$REPORT_FILE"
    echo "Files processed: $FILES_PROCESSED" >> "$REPORT_FILE"
    echo "Files modified: $FILES_MODIFIED" >> "$REPORT_FILE"
    echo "Total replacements: $REPLACEMENTS_MADE" >> "$REPORT_FILE"

    if [ "$DRY_RUN" = false ] && [ "$REPLACEMENTS_MADE" -gt 0 ]; then
        echo "Backup directory: $BACKUP_DIR" >> "$REPORT_FILE"
    fi

    echo "" >> "$REPORT_FILE"
    echo "URL PATTERNS REPLACED:" >> "$REPORT_FILE"
    echo "----------------------------------------" >> "$REPORT_FILE"
    for pattern in "${!URL_PATTERNS[@]}"; do
        echo "$pattern -> ${URL_PATTERNS[$pattern]}" >> "$REPORT_FILE"
    done
    echo "" >> "$REPORT_FILE"
    echo "================================================================================" >> "$REPORT_FILE"
}

main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --backup-dir)
                BACKUP_DIR="$2"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done

    echo "Starting Odoo URL replacement process..."
    echo "Mode: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "LIVE EXECUTION")"
    echo "Replacement URL: $REPLACEMENT_URL"

    if [ "$DRY_RUN" = false ]; then
        mkdir -p "$BACKUP_DIR"
        echo "Backup directory: $BACKUP_DIR"
    fi

    # Initialize report
    generate_report

    # Process each addon directory
    for addon_path in "${ADDON_PATHS[@]}"; do
        scan_directory "$addon_path"
    done

    # Final report
    echo ""
    echo "================================================================================"
    echo "SUMMARY"
    echo "================================================================================"
    echo "Files processed: $FILES_PROCESSED"
    echo "Files modified: $FILES_MODIFIED"
    echo "Total replacements: $REPLACEMENTS_MADE"

    if [ "$DRY_RUN" = false ] && [ "$REPLACEMENTS_MADE" -gt 0 ]; then
        echo "Backup directory: $BACKUP_DIR"
    fi

    # Update final report
    generate_report

    echo "Report saved to: $REPORT_FILE"

    if [ "$DRY_RUN" = true ]; then
        echo ""
        echo "This was a DRY RUN. No files were modified."
        echo "Run without --dry-run to apply changes."
    elif [ "$REPLACEMENTS_MADE" -gt 0 ]; then
        echo ""
        echo "URL replacement completed successfully!"
        echo "Remember to restart your Odoo services to apply the changes."
    else
        echo ""
        echo "No URLs found to replace."
    fi
}

# Run main function
main "$@"