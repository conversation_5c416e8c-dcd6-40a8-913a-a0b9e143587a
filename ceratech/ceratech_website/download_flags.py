import os
import requests

# <PERSON><PERSON><PERSON> le dossier flags s'il n'existe pas
flags_dir = os.path.join(os.path.dirname(__file__), 'static', 'src', 'img', 'flags')
os.makedirs(flags_dir, exist_ok=True)

# Liste des pays et leurs codes
countries = [
    ('sn', 'Sénégal'),
    ('cm', 'Cameroun'),
    ('fr', 'France'),
    ('ci', 'Côte d\'Ivoire'),
    ('eu', 'Zone Euro'),
    ('us', 'Zone Dollar'),
]

# Télécharger chaque drapeau
for code, name in countries:
    url = f'https://flagcdn.com/{code}.svg'
    response = requests.get(url)
    if response.status_code == 200:
        with open(os.path.join(flags_dir, f'{code}.svg'), 'wb') as f:
            f.write(response.content)
        print(f'Drapeau du {name} téléchargé avec succès')
    else:
        print(f'Erreur lors du téléchargement du drapeau du {name}')
