// Global variable to prevent infinite update loops
let isUpdating = false;

// Function to update calculations from the surface
function updateFromSurface(elements) {
    if (isUpdating) return;
    isUpdating = true;

    const desiredSurface = parseFloat(elements.surfaceInput.value) || 0;
    const { surfacePerItem, itemsPerCarton, pricePerCarton } = elements.data;

    let requiredItems = 0;
    if (surfacePerItem > 0) {
        requiredItems = Math.ceil(desiredSurface / surfacePerItem);
    }

    let requiredCartons = 0;
    if (itemsPerCarton > 0) {
        requiredCartons = Math.ceil(requiredItems / itemsPerCarton);
    }

    const totalPrice = requiredCartons * pricePerCarton;

    // Update the displays and the quantity field
    elements.piecesResultEl.textContent = requiredItems;
    elements.cartonsResultEl.textContent = requiredCartons;
    elements.totalPriceResultEl.value = totalPrice.toFixed(2);
    elements.quantityInput.value = requiredCartons;

    isUpdating = false;
}

// Function to update calculations from the quantity (cartons)
function updateFromQuantity(elements) {
    if (isUpdating) return;
    isUpdating = true;

    const requiredCartons = parseInt(elements.quantityInput.value) || 0;
    const { surfacePerItem, itemsPerCarton, pricePerCarton } = elements.data;

    const requiredItems = requiredCartons * itemsPerCarton;
    const totalSurface = requiredItems * surfacePerItem;
    const totalPrice = requiredCartons * pricePerCarton;

    // Update the displays and the surface field
    elements.surfaceInput.value = totalSurface.toFixed(2);
    elements.piecesResultEl.textContent = requiredItems;
    elements.cartonsResultEl.textContent = requiredCartons;
    elements.totalPriceResultEl.value = totalPrice.toFixed(2);

    isUpdating = false;
}

// Initialization on DOM load
document.addEventListener('DOMContentLoaded', function() {
    const surfaceInput = document.getElementById('surface_input');
    // Make sure we are on a page with the calculator
    if (!surfaceInput) return;

    const elements = {
        surfaceInput: surfaceInput,
        quantityInput: document.querySelector('input[name="add_qty"]'),
        piecesResultEl: document.getElementById('pieces_result'),
        cartonsResultEl: document.getElementById('cartons_result'),
        totalPriceResultEl: document.getElementById('total_price_result'),
        data: {
            surfacePerItem: parseFloat(document.getElementById('surface_per_item').textContent) || 0,
            itemsPerCarton: parseInt(document.getElementById('items_per_carton').textContent) || 1,
            pricePerCarton: parseFloat(document.getElementById('price_per_carton').textContent) || 0,
        }
    };

    // Check that all elements are present
    if (!elements.quantityInput || !elements.piecesResultEl || !elements.cartonsResultEl || !elements.totalPriceResultEl) {
        console.error('Some calculator elements are missing.');
        return;
    }

    // Attach event listeners
    elements.surfaceInput.addEventListener('input', () => updateFromSurface(elements));

    // We combine the 'input' listener (for the keyboard) and 'click' (for the +/- buttons)
    elements.quantityInput.addEventListener('input', () => updateFromQuantity(elements));

    // The +/- buttons are <a> tags in a parent div. We listen for clicks on this container.
    const quantityParent = elements.quantityInput.closest('.css_quantity');
    if (quantityParent) {
        quantityParent.addEventListener('click', (e) => {
            // We wait a short moment for the Odoo script to have time to update the input value
            setTimeout(() => {
                updateFromQuantity(elements);
            }, 50);
        });
    }

    // On load, we assume the quantity is 1 (carton)
    // and we calculate the corresponding surface.
    elements.quantityInput.value = 1;
    updateFromQuantity(elements);
});
