/** @odoo-module **/

import PublicWidget from '@web/legacy/js/public/public_widget';
import { jsonrpc } from '@web/core/network/rpc_service';
import { _t } from "@web/core/l10n/translation";

PublicWidget.registry.CountrySelector = PublicWidget.Widget.extend({
    selector: '#country_selector_widget',
    events: {
        'click .dropdown-item': '_onCountryChange',
    },

    start() {
        this._updateDisplayFromStorage();
        console.log(' Country Selector Widget Initialized');
        return this._super(...arguments);
    },

    _onCountryChange(ev) {
        ev.preventDefault();
        const $target = $(ev.currentTarget);

        // On récupère le texte directement depuis le span à l'intérieur du lien cliqué.
        const translatedText = $target.find('span').text().trim();
        const countryFlag = $target.data('country-flag');
        const currencyCode = $target.data('country-currency'); // Pour le backend

        // On sauvegarde le texte déjà formaté et traduit.
        localStorage.setItem('selectedCountryText', translatedText);
        localStorage.setItem('selectedCountryFlag', countryFlag);

        console.log(`Country selected: ${translatedText}`);

        // Call the controller to change the pricelist
        jsonrpc('/ceratech_website/update_pricelist', {
            currency_code: currencyCode
        }).then(result => {
            if (result.success) {
                window.location.reload();
            } else {
                console.error(_t('Error when changing currency:'), result.error);
            }
        });
    },

    _updateDisplayFromStorage() {
        // On lit et affiche directement le texte et le drapeau sauvegardés.
        const savedText = localStorage.getItem('selectedCountryText');
        const savedFlag = localStorage.getItem('selectedCountryFlag');
        if (savedText && savedFlag) {
            this.el.querySelector('.selected-country-name').textContent = savedText;
            const flagImg = this.el.querySelector('.selected-country-flag');
            flagImg.setAttribute('src', savedFlag);
            flagImg.setAttribute('alt', savedText);
        }
    },
});

export default PublicWidget.registry.CountrySelector;
