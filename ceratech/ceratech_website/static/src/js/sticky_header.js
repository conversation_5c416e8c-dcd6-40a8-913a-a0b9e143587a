document.addEventListener('DOMContentLoaded', () => {
    console.log('✅ Sticky header widget loaded');

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    const stickyHeader = document.getElementById('sticky_header');
    const scrollContainer = document.querySelector('#wrapwrap');
    const mainHeader = document.getElementById('top'); // More specific target for the observer

    if (!stickyHeader || !scrollContainer || !mainHeader) {
        console.error('Sticky Header: One of the essential elements is missing (#sticky_header, #wrapwrap, or #top).');
        return;
    }

    function cloneHeaderContent() {
        const topMenuSource = document.querySelector('#top_menu');
        const headerActionsSource = document.querySelector('.header-actions');
        const menuPlaceholder = stickyHeader.querySelector('#sticky_nav_menu');
        const iconsPlaceholder = stickyHeader.querySelector('.sticky-header-actions');

        if (topMenuSource && menuPlaceholder) {
            const clonedMenu = topMenuSource.cloneNode(true);
            clonedMenu.id = '';
            menuPlaceholder.innerHTML = '';
            menuPlaceholder.appendChild(clonedMenu);
            // Re-attach hover listeners for dropdowns inside the sticky header
            attachHoverListeners(menuPlaceholder);
        }

        if (headerActionsSource && iconsPlaceholder) {
            const clonedIcons = headerActionsSource.cloneNode(true);
            iconsPlaceholder.innerHTML = '';
            iconsPlaceholder.appendChild(clonedIcons);
        }
    }

    /**
     * Add hover behaviour to dropdown menu items within the given root.
     * This mirrors nav_hover.js but is applied to the cloned sticky header menu
     * because event listeners are not copied during cloneNode.
     */
    function attachHoverListeners(root) {
        if (!window.matchMedia('(min-width: 992px)').matches) {
            return; // Only on large screens
        }
        const dropdowns = root.querySelectorAll('.nav-item.dropdown');
        dropdowns.forEach((dropdownLi) => {
            // Skip if already handled
            if (dropdownLi.dataset.hoverHandled) return;
            dropdownLi.dataset.hoverHandled = 'true';

            let timeout;
            const toggle = dropdownLi.querySelector('[data-bs-toggle="dropdown"]');
            const menu = dropdownLi.querySelector('.dropdown-menu');
            if (!toggle || !menu) return;

            dropdownLi.addEventListener('mouseenter', () => {
                clearTimeout(timeout);
                dropdownLi.classList.add('show');
                menu.classList.add('show');
                toggle.setAttribute('aria-expanded', 'true');
            });
            dropdownLi.addEventListener('mouseleave', () => {
                timeout = setTimeout(() => {
                    dropdownLi.classList.remove('show');
                    menu.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }, 200);
            });
        });
    }

    function handleScroll() {
        const offset = 200;
        if (scrollContainer.scrollTop > offset) {
            stickyHeader.classList.add('is-sticky');
        } else {
            stickyHeader.classList.remove('is-sticky');
        }
    }

    function initializeSticky() {
        const menuFound = document.querySelector('#top_menu');
        const iconsFound = document.querySelector('.header-actions');

        if (menuFound && iconsFound) {
            cloneHeaderContent();
            scrollContainer.addEventListener('scroll', debounce(handleScroll, 15));
            handleScroll(); // Set initial state immediately
            return true; // Indicate success
        }
        return false; // Indicate failure
    }

    // 1. Attempt to initialize immediately
    if (!initializeSticky()) {
        // 2. If it fails, use a targeted observer as a fallback
        const observer = new MutationObserver((mutations, obs) => {
            if (initializeSticky()) {
                obs.disconnect(); // Success, so we stop observing
            }
        });

        observer.observe(mainHeader, { // Observe the specific header element, not the whole body
            childList: true,
            subtree: true
        });
    }
});
