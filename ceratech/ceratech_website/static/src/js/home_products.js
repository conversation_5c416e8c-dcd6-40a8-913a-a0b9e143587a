/** @odoo-module **/

import PublicWidget from '@web/legacy/js/public/public_widget';
import { jsonrpc } from '@web/core/network/rpc_service';
import { renderToFragment } from '@web/core/utils/render';
import { _t } from "@web/core/l10n/translation";

PublicWidget.registry.CeratechDynamicProducts = PublicWidget.Widget.extend({
    selector: '.s_ceratech_products_section',

    async start() {
        await this._super(...arguments);
        const data = await jsonrpc('/ceratech_website/get_products', {});
        this._renderProducts(data.products, data.currency);
    },

    _renderProducts(products, currency) {
        const container = this.el.querySelector('#dynamic_product_container');
        if (!container) {
            return;
        }
        
        container.innerHTML = ''; // Clear previous content

        if (!products || products.length === 0) {
            container.innerHTML = `<div class="col-12 text-center"><p>${_t("No products to display.")}</p></div>`;
            return;
        }

        products.forEach(product => {
            const productCard = renderToFragment('ceratech_website.ProductCard', {
                product: product,
                currency: currency,
            });
            container.appendChild(productCard);
        });
        console.log('✅ Homepage products loaded');
    },
});

export default PublicWidget.registry.CeratechDynamicProducts;
