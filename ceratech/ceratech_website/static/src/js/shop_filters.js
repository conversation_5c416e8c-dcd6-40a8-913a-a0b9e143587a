document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ Custom produts filters Loaded');
    // Target the necessary elements
    const priceFilter = document.querySelector('#o_wsale_price_range_option');
    const attributesContainer = document.querySelector('#wsale_products_attributes_collapse');

    // Check that the elements exist on the page before continuing
    if (priceFilter && attributesContainer) {
        // 1. Make the price filter visible by removing the class that hides it
        priceFilter.classList.remove('d-none');
        
        // 2. Move the price filter just before the attributes container
        attributesContainer.parentNode.insertBefore(priceFilter, attributesContainer);
    }
});
