document.addEventListener('DOMContentLoaded', function () {
    console.log('✅ Ceratech Nav Hover JS Loaded');
    // S'assurer que le script ne s'exécute que sur les grands écrans
    if (window.matchMedia('(min-width: 992px)').matches) {

        const dropdowns = document.querySelectorAll('.main-navigation .nav-item.dropdown');

        dropdowns.forEach(function(dropdownLi) {
            let timeout;
            const toggle = dropdownLi.querySelector('[data-bs-toggle="dropdown"]');
            const menu = dropdownLi.querySelector('.dropdown-menu');

            // Sécurité : ne rien faire si les éléments n'existent pas
            if (!toggle || !menu) {
                return;
            }

            dropdownLi.addEventListener('mouseenter', function () {
                clearTimeout(timeout);
                // Manually show the dropdown
                dropdownLi.classList.add('show');
                menu.classList.add('show');
                toggle.setAttribute('aria-expanded', 'true');
            });

            dropdownLi.addEventListener('mouseleave', function () {
                timeout = setTimeout(function () {
                    // Manually hide the dropdown
                    dropdownLi.classList.remove('show');
                    menu.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }, 200);
            });
        });
    }
});
