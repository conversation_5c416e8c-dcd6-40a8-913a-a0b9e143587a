/** @odoo-module **/

import PublicWidget from '@web/legacy/js/public/public_widget';
import { jsonrpc } from '@web/core/network/rpc_service';
import { _t } from "@web/core/l10n/translation";

export const QuoteFromCart = PublicWidget.Widget.extend({
    selector: '#wrapwrap', // We attach to a parent element that always exists
    events: {
        // Events are delegated, they will work for dynamically added elements
        'click #get_quote_button': '_onGetQuoteClick',
    },

    /**
     * @override
     */
    start() {
        const def = this._super(...arguments);
        // Only runs on the cart page
        if (window.location.pathname.includes('/shop/cart')) {
            console.log('QuoteFromCart: Initializing on cart page.');
            this._insertModal();
            this._insertQuoteButton();
        }
        return def;
    },

    //--------------------------------------------------------------------------
    // Private
    //--------------------------------------------------------------------------

    /**
     * Injects the 'Generate a quote' button in the right place.
     */
    _insertQuoteButton() {
        if (document.getElementById('get_quote_button')) {
            return; // The button already exists
        }
        const target = document.querySelector('#o_cart_summary .o_total_card');
        if (target) {
            console.log('QuoteFromCart: Inserting quote button.');
            const labelGenerate = _t('Generate quotation');
            const buttonHtml = `
                <div class="d-grid gap-2 my-3 px-lg-4">
                    <a id="get_quote_button" href="#" class="btn btn-secondary">${labelGenerate}</a>
                </div>`;
            target.insertAdjacentHTML('beforebegin', buttonHtml);
        } else {
            // If the target is not found immediately, we use an observer.
            const observer = new MutationObserver((mutations, obs) => {
                const targetElement = document.querySelector('#o_cart_summary .o_total_card');
                if (targetElement) {
                    this._insertQuoteButton(); // We call the function again to do the injection
                    obs.disconnect(); // We stop observing once it's done
                }
            });
            observer.observe(document.body, { childList: true, subtree: true });
        }
    },

    /**
     * Creates and inserts the modal's HTML into the body.
     */
    _insertModal() {
        // If the modal is already there, we just make sure the instance is ready for the click.
        if (document.getElementById('quote_details_modal')) {
            return;
        }
        console.log('QuoteFromCart: Inserting modal into DOM.');

        const modalTitle = _t('Get my quotation');
        const modalBodyText = _t('To generate your quotation, please provide your contact details. The PDF will start downloading immediately.');
        const fullNameLabel = _t('Full name');
        const nameError = _t('Please enter your name.');
        const emailLabel = _t('Email address');
        const emailError = _t('Please enter a valid email address.');
        const phoneLabel = _t('Phone number');
        const phoneError = _t('Please enter your phone number.');
        const cancelButton = _t('Cancel');
        const downloadButton = _t('Download my quotation');

        const modalHtml = `
            <div class="modal fade" id="quote_details_modal" tabindex="-1" aria-labelledby="quoteModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="quoteModalLabel">${modalTitle}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>${modalBodyText}</p>
                            <form id="quote_details_form" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label for="quote_name" class="form-label">${fullNameLabel}</label>
                                    <input type="text" class="form-control" id="quote_name" required>
                                    <div class="invalid-feedback">${nameError}</div>
                                </div>
                                <div class="mb-3">
                                    <label for="quote_email" class="form-label">${emailLabel}</label>
                                    <input type="email" class="form-control" id="quote_email" required>
                                    <div class="invalid-feedback">${emailError}</div>
                                </div>
                                <div class="mb-3">
                                    <label for="quote_phone" class="form-label">${phoneLabel}</label>
                                    <input type="tel" class="form-control" id="quote_phone" required>
                                    <div class="invalid-feedback">${phoneError}</div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">${cancelButton}</button>
                            <button id="submit_quote_request" type="button" class="btn btn-primary">${downloadButton}</button>
                        </div>
                    </div>
                </div>
            </div>`;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Explicitly attach the submission handler, as the modal is outside of #wrapwrap
        const submitBtn = document.getElementById('submit_quote_request');
        if (submitBtn) {
            submitBtn.addEventListener('click', this._onSubmitQuoteRequest.bind(this));
        }

        // We let the _onGetQuoteClick handler initialize the modal on the first click
        // to make sure everything is loaded.
    },

    //--------------------------------------------------------------------------
    // Handlers
    //--------------------------------------------------------------------------

    _onGetQuoteClick(ev) {
        ev.preventDefault();
        const isUserLoggedIn = odoo.is_logged_in;

        if (isUserLoggedIn) {
            this._createQuoteAndDownload();
        } else {
            const modalElement = document.getElementById('quote_details_modal');
            if (!modalElement) {
                console.error('Modal element could not be found in DOM.');
                return;
            }
            // JIT (Just-In-Time) initialization of the modal
            if (!this.quoteModal) {
                if (window.bootstrap && window.bootstrap.Modal) {
                    this.quoteModal = new window.bootstrap.Modal(modalElement);
                } else if (typeof jQuery !== 'undefined' && typeof jQuery.fn.modal === 'function') {
                    this.quoteModal = {
                        show: () => jQuery(modalElement).modal('show'),
                        hide: () => jQuery(modalElement).modal('hide'),
                    };
                } else {
                    console.error('Bootstrap Modal component not found.');
                    alert(_t("Cannot open the dialog for now. Please refresh the page."));
                    return;
                }
            }
            this.quoteModal.show();
        }
    },

    _onSubmitQuoteRequest(ev) {
        ev.preventDefault();
        const form = document.getElementById('quote_details_form');
        const name = document.getElementById('quote_name').value;
        const email = document.getElementById('quote_email').value;
        const phone = document.getElementById('quote_phone').value;

        if (form.checkValidity() === false) {
            ev.stopPropagation();
            form.classList.add('was-validated');
        } else {
            this._createQuoteAndDownload({ partner_name: name, partner_email: email, partner_phone: phone });
        }
    },

    async _createQuoteAndDownload(partnerData = {}) {
        const submitButton = document.getElementById('submit_quote_request');
        const getQuoteButton = document.getElementById('get_quote_button');
        
        if(submitButton) submitButton.setAttribute('disabled', 'disabled');
        if(getQuoteButton) getQuoteButton.classList.add('disabled');
        if(submitButton) submitButton.insertAdjacentHTML('afterbegin', '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>');

        try {
            const result = await jsonrpc('/shop/create_quote', partnerData);
            if (result.error) {
                alert(_t('Error: ') + result.error);
            } else {
                if (this.quoteModal) this.quoteModal.hide();
                window.location = result.download_url;
            }
        } catch (error) {
            alert(_t('An unexpected error occurred. Please try again.'));
            console.error("Quote creation failed", error);
        } finally {
            if(submitButton) {
                submitButton.removeAttribute('disabled');
                const spinner = submitButton.querySelector('span');
                if(spinner) spinner.remove();
            }
            if(getQuoteButton) getQuoteButton.classList.remove('disabled');
        }
    },
});

PublicWidget.registry.QuoteFromCart = QuoteFromCart;
export default QuoteFromCart;
