/* Fichier principal pour les styles SCSS personnalisés */

//* Effet de survol pour les liens de navigation */
.main-navigation .nav-item .nav-link:hover {
    font-weight: var(--bs-font-weight-semibold, 600);
    color: var(--o-color-1) !important; /* Utilisation de la variable CSS de la couleur primaire */
}

/* General Typography */
h2 {
    color: var(--o-color-1);
}

// Homepage Carousel Snippet
.s_ceratech_homepage_carousel {
    .carousel-item {
        position: relative;

        // Add a dark overlay for better text readability
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.4);
            z-index: 1;
        }

        .container {
            position: relative;
            z-index: 2;
        }
    }
}

// Categories Section Snippet
.s_ceratech_categories_section {
    .category-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: .5rem;

        img {
            border-top-left-radius: .5rem;
            border-top-right-radius: .5rem;
            object-fit: cover;
        }

        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            color: #495057; // A dark grey color for text
            font-size: 1rem;
        }
    }
}

// Products Section Snippet
.s_ceratech_products_section {
    .s_products_title {
        font-weight: 800;
        color: var(--o-color-1); // Bootstrap primary blue
        font-size: 2.8rem;
        text-transform: uppercase;
    }

    .product-name {
        font-size: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: 0.25rem;
    }

    .product-list-price {
        font-size: 0.9rem;
        font-weight: 400;
    }

    .product-price {
        color: var(--o-color-1); // Bootstrap primary blue
        font-size: 1rem;
        font-weight: 700;
    }

    .product-stock {
        font-size: 0.8rem;
        font-weight: 500;
    }
}

// Products Section Snippet
.s_products_search .o_wsale_products_grid_card {
    border: none !important;
    box-shadow: none !important;

    .o_wsale_product_information_text {
        text-align: left;
        padding-top: 0.75rem;

        .o_wsale_product_name {
            font-weight: 700;
            text-transform: uppercase;
            font-size: 1rem;
            color: #212529;
            margin-bottom: 0.25rem;
        }

        .product_price {
            .text-danger,
            del {
                font-size: 0.85rem;
                text-decoration: line-through;
                font-weight: normal;
                color: #6c757d !important;
                margin-right: 0.5rem;
            }

            .oe_price .oe_currency_value {
                color: var(--o-color-1);
                font-weight: 700;
                font-size: 1.1rem;
            }
        }
    }
}

// Styles for Conception 3D Section
.s_conception_3d {
  background-size: cover;
  background-position: center center;
  min-height: 600px;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .s_conception_3d_content {
    background-color: rgba(255, 255, 255, 0.85);
    padding: 4rem 2rem;
    border-radius: 0.5rem;
    width: 80%; // Largeur relative
    max-width: 800px; // Largeur maximale
    text-align: center;
  }

  .s_conception_3d_title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  .lead {
    color: #212529; // Bootstrap default text color
    font-size: 1.25rem;
  }
}

// Partner Scroller Styles
.s_partners_title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

@keyframes scroll-left {
    from { transform: translateX(0); }
    to { transform: translateX(-50%); }
}

@keyframes scroll-right {
    from { transform: translateX(-50%); }
    to { transform: translateX(0); }
}

.s_partners_scroller {
    overflow: hidden;
    -webkit-mask: linear-gradient(90deg, transparent, white 20%, white 80%, transparent);
    mask: linear-gradient(90deg, transparent, white 20%, white 80%, transparent);
    margin-bottom: 1rem;

    .scroller_inner {
        display: flex;
        flex-wrap: nowrap;
        width: max-content;
        animation: scroll-left 40s linear infinite;

        img {
            height: 60px; // Hauteur du logo lui-même
            border: 2px solid #ddd; // Bordure grise claire
            border-radius: 15px; // Coins arrondis
            box-sizing: content-box; // Assure que le padding s'ajoute à la hauteur
            margin: 0 40px;
            filter: grayscale(100%);
            transition: all 0.3s ease;
            opacity: 0.8;

            &:hover {
                filter: grayscale(0%);
                opacity: 1;
                border-color: #aaa;
                box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            }
        }
    }

    &[data-direction="right"] .scroller_inner {
        animation-name: scroll-right;
    }
}

// About Us Section Styles
.s_about_us {
    .s_about_us_title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
    }

    .btn-outline-primary {
        padding: 0.75rem 1.5rem;
        border-radius: 50rem; // Pill shape
        font-weight: 600;
        border-width: 2px;
    }
}

// Trust Section Styles
.s_trust {
    .s_trust_title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    .s_trust_feature {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        font-size: 1.1rem;
        font-weight: 500;

        i {
            width: 50px; // To ensure alignment
            margin-right: 1.5rem;
        }
    }
}

// Location Section Styles
.s_location {
    .s_location_title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
    }

    .map_container {
        height: 450px;
        .s_map_frame, .s_map_color_filter, iframe {
            height: 100% !important;
        }
    }
}
