/*
Ce fichier surcharge les variables de couleur et de police du système de thème d'Odoo.
Il définit la palette personnalisée "ceratech" et applique la police "Playfair Display" uniquement aux titres.
*/

// 1. Déclaration de la palette de couleurs personnalisée
$o-color-palettes: map-merge($o-color-palettes,
  (
    'ceratech': (
      'o-color-1': #2c75ff,    // Primaire (bleu Ceratech)
      'o-color-2': #6c757d,    // Secondaire (gris)
      'o-color-3': #f8f9fa,    // Extra (gris clair)
      'o-color-4': #ffffff,    // Blanc
      'o-color-5': #343a40,    // Noir doux
    ),
  )
);

// 2. Ajouter la palette à la liste sélectionnable
$o-selected-color-palettes-names: append($o-selected-color-palettes-names, 'ceratech');

// 3. Déclarer la police Google "Playfair Display"
$o-theme-font-configs: map-merge($o-theme-font-configs,
   (
      'Playfair Display': (
         'family': ('Playfair Display', serif),
         'url': 'Playfair+Display:300,400,700',
         'properties': (
            'base': (
            'font-size-base': 1rem,
            ),
         ),
      ),
   )
);

// 4. Définir la palette par défaut et appliquer la police uniquement aux titres
$o-website-values-palettes: (
  (
    'color-palettes-name': 'ceratech',
    'headings-font': 'Playfair Display',
  ),
);
