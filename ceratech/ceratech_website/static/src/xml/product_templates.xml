<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="ceratech_website.ProductCard">
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4 d-flex align-items-stretch">
            <div class="card border-0 h-100 bg-transparent">
                <a t-attf-href="/shop/product/{{ product.id }}" class="d-block">
                    <img class="card-img-top" t-att-src="product.image_url" style="aspect-ratio: 1/1; object-fit: cover;" alt="Product Image"/>
                </a>
                <div class="card-body text-start p-0 pt-3">
                    <h5 class="product-name mb-1">
                        <a t-attf-href="/shop/product/{{ product.id }}" t-esc="product.name" class="text-dark text-decoration-none"/>
                    </h5>
                    <div class="d-flex flex-wrap align-items-center">
                        <div class="product-prices me-auto">
                            <t t-if="product.list_price > product.price">
                                <del class="product-list-price text-muted d-block">
                                    <t t-if="currency.position === 'before'">
                                        <span t-esc="currency.symbol"/>
                                        <span t-esc="Math.round(product.list_price)"/>
                                    </t>
                                    <t t-if="currency.position === 'after'">
                                        <span t-esc="Math.round(product.list_price)"/>
                                        <span t-esc="currency.symbol"/>
                                    </t>
                                </del>
                            </t>
                            <div class="product-price fw-bold">
                                <t t-if="currency.position === 'before'">
                                    <span t-esc="currency.symbol"/>
                                    <span t-esc="Math.round(product.price)"/>
                                </t>
                                <t t-if="currency.position === 'after'">
                                    <span t-esc="Math.round(product.price)"/>
                                    <span t-esc="currency.symbol"/>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
