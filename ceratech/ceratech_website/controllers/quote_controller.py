import logging
import base64
from odoo import http, _
from odoo.http import request

_logger = logging.getLogger(__name__)

class QuoteFromCartController(http.Controller):

    @http.route('/shop/create_quote', type='json', auth='public', website=True, csrf=True)
    def create_quote_from_cart(self, partner_name=None, partner_email=None, partner_phone=None, **kwargs):
        try:
            # 1. Get the current cart (sale.order)
            order = request.website.sale_get_order(force_create=False)
            if not order or not order.order_line:
                return {'error': _('Your cart is empty.')}

            partner = order.partner_id

            # 2. Handle guest user: find or create partner
            if request.website.is_public_user():
                if not partner_name or not partner_email or not partner_phone:
                    return {'error': _('Name, email and phone are required for guests.')}
                
                partner = request.env['res.partner'].sudo().search([
                    ('email', '=', partner_email)
                ], limit=1)
                
                partner_vals = {
                    'name': partner_name,
                    'email': partner_email,
                    'phone': partner_phone,
                    'company_type': 'person',
                }

                if not partner:
                    partner = request.env['res.partner'].sudo().create(partner_vals)
                else:
                    # Update phone if partner already exists but phone is not set
                    if not partner.phone:
                        partner.sudo().write({'phone': partner_phone})
                
                # Assign partner to the order and the session
                order.sudo().write({'partner_id': partner.id})
                request.session['sale_order_id'] = order.id

            # 3. Create CRM Lead/Opportunity (ignore team errors for public user)
            try:
                team_record = request.env['crm.team'].sudo()._get_default_team_id(user_id=request.env.user.id)
                default_team_id = team_record.id if team_record else False
            except Exception:
                default_team_id = False

            utm_source = request.env.ref('utm.utm_source_website', raise_if_not_found=False)
            lead_vals = {
                'name': _('Website quotation - %(order)s for %(partner)s') % {
                    'order': order.name,
                    'partner': partner.name
                },
                'partner_id': partner.id,
                'type': 'opportunity',
                'team_id': default_team_id,
                'order_ids': [(4, order.id)],
            }
            if utm_source:
                lead_vals['source_id'] = utm_source.id
            request.env['crm.lead'].sudo().create(lead_vals)

            # 4. Generate the PDF report
            pdf, __ = request.env['ir.actions.report'].sudo()._render_qweb_pdf('sale.action_report_saleorder', [order.id])

            # 5. Create an attachment to make it downloadable
            attachment = request.env['ir.attachment'].sudo().create({
                'name': _('Quote_%s.pdf') % order.name,
                'type': 'binary',
                'datas': base64.b64encode(pdf),
                'res_model': 'sale.order',
                'res_id': order.id,
                'mimetype': 'application/pdf',
                'public': True,
            })

            # Generate an access_token for extra safety
            attachment.generate_access_token()

            download_url = f"/web/content/{attachment.id}?download=true&access_token={attachment.access_token}"

            # The cart should not be cleared automatically, let the user decide.
            # request.session['sale_order_id'] = None

            return {'download_url': download_url}

        except Exception as e:
            _logger.error("Error creating quote from cart: %s", e, exc_info=True)
            return {'error': _('A server error occurred. Unable to generate the quotation.')}

    # ---------------------------------------------------------------------
    # Checkout link from PDF
    # ---------------------------------------------------------------------

    @http.route(['/quote/checkout/<int:order_id>/<string:access_token>'], type='http', auth='public', website=True, csrf=False)
    def quote_checkout(self, order_id, access_token, **kwargs):
        """Restore the given sale order in session and redirect to /shop/checkout."""
        order = request.env['sale.order'].sudo().browse(order_id)
        if not order or order.access_token != access_token:
            return request.not_found()

        # Set the order in the current session
        request.session['sale_order_id'] = order.id

        # If user logged in and order has different partner, keep order's partner.
        # Odoo will handle permissions on /shop/checkout.

        return request.redirect('/shop/checkout')
