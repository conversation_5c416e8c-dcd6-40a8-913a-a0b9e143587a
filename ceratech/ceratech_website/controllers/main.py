from odoo import http
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)

class WebsiteCeratechController(http.Controller):

    @http.route('/ceratech_website/get_products', type='json', auth='public', website=True)
    def get_products(self, **kw):
        products = request.env['product.template'].search(
            [('is_published', '=', True)],
            limit=8,
            order='website_sequence asc'
        )
        
        product_data = []

        for product in products:
            product_data.append({
                'id': product.id,
                'name': product.name,
                'price': product.list_price,  # Prix normal
                'list_price': product.list_price,  # Prix public (pour comparaison)
                'image_url': f'/web/image/product.template/{product.id}/image_256',
            })

        currency = request.website.currency_id
        return {
            'products': product_data,
            'currency': {
                'symbol': currency.symbol,
                'position': currency.position,
            }
        }

    @http.route('/about-us', type='http', auth='public', website=True)
    def about_us(self, **kw):
        """ Route pour la page 'À propos de nous' """
        return request.render('ceratech_website.about_us_page')

    @http.route('/partners', type='http', auth='public', website=True)
    def partners(self, **kw):
        return request.render('ceratech_website.partners_page', {})
