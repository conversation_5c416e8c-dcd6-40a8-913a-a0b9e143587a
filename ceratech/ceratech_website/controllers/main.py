from odoo import http
from odoo.http import request
import json
import logging
from odoo import _

_logger = logging.getLogger(__name__)

class WebsiteCeratechController(http.Controller):

    @http.route('/ceratech_website/get_products', type='json', auth='public', website=True)
    def get_products(self, **kw):
        products = request.env['product.template'].search(
            [('is_published', '=', True)],
            limit=8,
            order='website_sequence asc'
        )
        
        product_data = []

        for product in products:
            product_data.append({
                'id': product.id,
                'name': product.name,
                'price': product.list_price,  # Normal price
                'list_price': product.list_price,  # Public price (for comparison)
                'image_url': f'/web/image/product.template/{product.id}/image_256',
            })

        currency = request.website.currency_id
        return {
            'products': product_data,
            'currency': {
                'symbol': currency.symbol,
                'position': currency.position,
            }
        }

    @http.route('/about-us', type='http', auth='public', website=True)
    def about_us(self, **kw):
        """ Route for the 'About Us' page """
        return request.render('ceratech_website.about_us_page')

    @http.route('/partners', type='http', auth='public', website=True)
    def partners(self, **kw):
        return request.render('ceratech_website.partners_page', {})

class CeratechWebsite(http.Controller):

    @http.route(['/ceratech_website/update_pricelist'], type='json', auth='public', website=True)
    def update_pricelist(self, currency_code, **kwargs):
        if not currency_code:
            return {'success': False, 'error': _('Currency code is missing.')}

        # Find the corresponding currency
        Currency = request.env['res.currency']
        currency = Currency.search([('name', '=', currency_code)], limit=1)
        if not currency:
            return {'success': False, 'error': _('Currency %s not found.') % currency_code}

        # Find a pricelist for this currency
        Pricelist = request.env['product.pricelist']
        pricelist = Pricelist.search([
            ('currency_id', '=', currency.id),
            '|', ('website_id', '=', request.website.id),
                 ('website_id', '=', False)
        ], limit=1)

        if not pricelist:
            return {'success': False, 'error': _('No pricelist found for currency %s.') % currency_code}

        # Update the pricelist in the user's session
        request.session['website_sale_current_pl'] = pricelist.id

        return {'success': True}
