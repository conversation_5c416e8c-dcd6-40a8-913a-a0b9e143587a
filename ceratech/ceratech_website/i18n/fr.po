# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ceratech_website
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-25 10:01+0000\n"
"PO-Revision-Date: 2025-07-25 10:01+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_contactus_section
msgid "(+221) 77 347 02 02"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "0.00"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "2 cm Thick Stoneware"
msgstr "Grès Cérame 2 cm d'Épaisseur"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_conception_3d_section
msgid "3D Design"
msgstr "Conception 3D"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "3D Render"
msgstr "Rendu 3D"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid ""
"<i class=\"fa fa-archive me-2\"/> Required cartons: <strong "
"id=\"cartons_result\">0</strong>"
msgstr ""
"<i class=\"fa fa-archive me-2\"/> Cartons requis : <strong "
"id=\"cartons_result\">0</strong>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid ""
"<i class=\"fa fa-map-marker me-2\"/>Address: Av, Yacinthe Thiandoum - Scat "
"Urbam<br/>N° 08 -BP: 17181 Dakar Liberté"
msgstr ""
"<i class=\"fa fa-map-marker me-2\"/>Adresse : Av, Yacinthe Thiandoum - Scat "
"Urbam<br/>N° 08 - BP : 17181 Dakar Liberté"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fa fa-phone me-2\"/>(+221) 77 347 02 02"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid ""
"<i class=\"fa fa-th-large me-2\"/> Required pieces: <strong "
"id=\"pieces_result\">0</strong>"
msgstr ""
"<i class=\"fa fa-th-large me-2\"/> Pièces requises : <strong "
"id=\"pieces_result\">0</strong>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_trust_section
msgid ""
"<i class=\"fas fa-box-open fa-2x text-primary\"/>\n"
"                                <span>Free samples</span>"
msgstr ""
"<i class=\"fas fa-box-open fa-2x text-primary\"/>\n"
"                                <span>Échantillons gratuits</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-box-open me-2\"/>Delivery"
msgstr "<i class=\"fas fa-box-open me-2\"/>Livraison"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-check me-2\"/>Product Advice"
msgstr "<i class=\"fas fa-check me-2\"/>Conseils produits"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-check me-2\"/>Request a sample"
msgstr "<i class=\"fas fa-check me-2\"/>Demander un échantillon"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-check me-2\"/>Special Orders"
msgstr "<i class=\"fas fa-check me-2\"/>Commandes spéciales"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-check-circle me-2\"/>Compliance"
msgstr "<i class=\"fas fa-check-circle me-2\"/>Conformité"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_trust_section
msgid ""
"<i class=\"fas fa-credit-card fa-2x text-primary\"/>\n"
"                                <span>Secure payment</span>"
msgstr ""
"<i class=\"fas fa-credit-card fa-2x text-primary\"/>\n"
"                                <span>Paiement sécurisé</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-envelope me-2\"/>Contact Email:"
msgstr "<i class=\"fas fa-envelope me-2\"/>E-mail de contact :"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-envelope me-2\"/>Newsletter"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-exclamation-circle me-2\"/>Report a Problem"
msgstr "<i class=\"fas fa-exclamation-circle me-2\"/>Signaler un problème"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-file-contract me-2\"/>Terms &amp; Conditions"
msgstr "<i class=\"fas fa-file-contract me-2\"/>Termes &amp; Conditions"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-file-invoice-dollar me-2\"/>Invoice"
msgstr "<i class=\"fas fa-file-invoice-dollar me-2\"/>Facture"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-gift me-2\"/>Gift Vouchers"
msgstr "<i class=\"fas fa-gift me-2\"/>Bons cadeaux"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_trust_section
msgid ""
"<i class=\"fas fa-headset fa-2x text-primary\"/>\n"
"                                <span>Responsive customer service</span>"
msgstr ""
"<i class=\"fas fa-headset fa-2x text-primary\"/>\n"
"                                <span>Service client réactif</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-headset me-2\"/>Order, Service &amp; Advice"
msgstr "<i class=\"fas fa-headset me-2\"/>Commande, Service &amp; Conseil"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-info-circle me-2\"/>About Us"
msgstr "<i class=\"fas fa-info-circle me-2\"/>À propos de nous"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid ""
"<i class=\"fas fa-phone-alt me-2\"/>\n"
"                                            (+221) 77 347 02 02"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_trust_section
msgid ""
"<i class=\"fas fa-shield-alt fa-2x text-primary\"/>\n"
"                                <span>Anti-breakage guarantee</span>"
msgstr ""
"<i class=\"fas fa-shield-alt fa-2x text-primary\"/>\n"
"                                <span>Garantie anti-casse</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-shield-alt me-2\"/>Secure Payment"
msgstr "<i class=\"fas fa-shield-alt me-2\"/>Paiement sécurisé"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-shipping-fast me-2\"/>Shipping Costs"
msgstr "<i class=\"fas fa-shipping-fast me-2\"/>Frais de livraison"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-sitemap me-2\"/>Site Map"
msgstr "<i class=\"fas fa-sitemap me-2\"/>Plan du site"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_trust_section
msgid ""
"<i class=\"fas fa-star fa-2x text-primary\"/>\n"
"                                <span>Top brand products</span>"
msgstr ""
"<i class=\"fas fa-star fa-2x text-primary\"/>\n"
"                                <span>Produits de grandes marques</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_trust_section
msgid ""
"<i class=\"fas fa-truck fa-2x text-primary\"/>\n"
"                                <span>Fast &amp; careful delivery</span>"
msgstr ""
"<i class=\"fas fa-truck fa-2x text-primary\"/>\n"
"                                <span>Livraison rapide &amp; soignée</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid ""
"<i class=\"fas fa-truck me-2\"/>\n"
"                                            <span>Delivery</span>"
msgstr ""
"<i class=\"fas fa-truck me-2\"/>\n"
"                                            <span>Livraison</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<i class=\"fas fa-user-secret me-2\"/>Privacy Policy"
msgstr "<i class=\"fas fa-user-secret me-2\"/>Politique de confidentialité"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid ""
"<span class=\"carousel-control-next-icon\" aria-hidden=\"true\"/>\n"
"                    <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\" aria-hidden=\"true\"/>\n"
"                    <span class=\"visually-hidden\">Suivant</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"/>\n"
"                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"/>\n"
"                    <span class=\"visually-hidden\">Précédent</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<span class=\"selected-country-name\">Senegal (XOF)</span>"
msgstr "<span class=\"selected-country-name\">Sénégal (XOF)</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.footer_copyright_ceratech
msgid "<span class=\"text-muted small\">Copyright © Ceratech</span>"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<span>Cameroon (XAF)</span>"
msgstr "<span>Cameroun (XAF)</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<span>Dollar (USD)</span>"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<span>Euro (EUR)</span>"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<span>Ivory Coast (XOF)</span>"
msgstr "<span>Côte d'Ivoire (XOF)</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "<span>Senegal (XOF)</span>"
msgstr "<span>Sénégal (XOF)</span>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_contactus_section
msgid "<strong>Address:</strong>"
msgstr "<strong>Adresse :</strong>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_contactus_section
msgid "<strong>Email:</strong>"
msgstr "<strong>E-mail :</strong>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_contactus_section
msgid "<strong>Phone:</strong>"
msgstr "<strong>Téléphone :</strong>"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid ""
"A UNIQUE STYLE<br/>\n"
"                                FOR YOUR INTERIOR"
msgstr ""
"UN STYLE UNIQUE<br/>\n"
"                                POUR VOTRE INTÉRIEUR"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/controllers/quote_controller.py:0
#, python-format
msgid "A server error occurred. Unable to generate the quotation."
msgstr "Une erreur de serveur s'est produite. Impossible de générer le devis."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "About Ceratech"
msgstr "À propos de Ceratech"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "About Us"
msgstr "À propos de nous"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Alaplana céramica"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "All Effects"
msgstr "Tous les effets"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "An unexpected error occurred. Please try again."
msgstr "Une erreur inattendue s'est produite. Veuillez réessayer."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Antica ceramica rubiera"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Atlas concorde"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_contactus_section
msgid "Av, Yacinthe Thiandoum - Scat Urbam<br/>N° 08 -BP: 17181 Dakar Liberté"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Azulejos benadresa"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Bathroom"
msgstr "Salle de bain"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Belite ceramics"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid ""
"Bring your floors and walls to life with our collections of aesthetic, durable, and easy-to-maintain tiles.<br/>\n"
"                                For every room, a style that suits you."
msgstr ""
"Donnez vie à vos sols et murs avec nos collections de carrelages esthétiques, durables et faciles d'entretien.<br/>\n"
"                                À chaque pièce, un style qui vous ressemble."

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/models/product_template.py:0
#: model:ir.model.fields,help:ceratech_website.field_product_product__surface_per_item
#: model:ir.model.fields,help:ceratech_website.field_product_template__surface_per_item
#, python-format
msgid "Calculated surface for a single item."
msgstr "Surface calculée pour un seul article."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Cameroon"
msgstr "Cameroun"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Cancel"
msgstr "Annuler"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Cannot open the dialog for now. Please refresh the page."
msgstr ""
"Impossible d'ouvrir la boîte de dialogue pour le moment. Veuillez rafraîchir"
" la page."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Cement Tile Look"
msgstr "Imitation Carreau de Ciment"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_about_us_section
msgid ""
"CeraTech is a team passionate about design and sustainable materials. Since "
"our beginnings, we have been supporting individuals and professionals in "
"transforming their spaces."
msgstr ""
"CeraTech est une équipe passionnée par le design et les matériaux durables. "
"Depuis nos débuts, nous accompagnons particuliers et professionnels dans la "
"transformation de leurs espaces."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Ceramic Wall Tiles"
msgstr "Carrelage Mural en Céramique"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Ceratech Logo"
msgstr "Logo Ceratech"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_location_section
msgid ""
"Come and discover our products directly in the showroom and benefit from "
"personalized advice. Our experts welcome you in Dakar."
msgstr ""
"Venez découvrir nos produits directement en showroom et bénéficiez de "
"conseils personnalisés. Nos experts vous accueillent à Dakar."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Company"
msgstr "Entreprise"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Concrete Look"
msgstr "Imitation Béton"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Contact"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.contactus_custom
msgid "Contact Us"
msgstr "Contactez-nous"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.report_saleorder_with_checkout_link
msgid "Continue online payment"
msgstr "Continuer le paiement en ligne"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/controllers/main.py:0
#, python-format
msgid "Currency %s not found."
msgstr "Devise %s non trouvée."

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/controllers/main.py:0
#, python-format
msgid "Currency code is missing."
msgstr "Le code de la devise est manquant."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "Dedicated Customer Service"
msgstr "Service Client Dédié"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Delivery &amp; Payment"
msgstr "Livraison &amp; Paiement"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "Discover our new products"
msgstr "Découvrez nos nouveautés"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "Discover our product catalog and get inspired."
msgstr "Découvrez notre catalogue de produits et laissez-vous inspirer."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Dollar"
msgstr ""

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Download my quotation"
msgstr "Télécharger mon devis"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Email address"
msgstr "Adresse e-mail"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/country_selector.js:0
#, python-format
msgid "Error when changing currency:"
msgstr "Erreur lors du changement de devise :"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Error: "
msgstr "Erreur : "

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Euro"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "Exceptional Brands"
msgstr "Marques d'Exception"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "Explore our categories"
msgstr "Explorez nos catégories"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "Explore our exclusive ranges and find inspiration for your projects."
msgstr ""
"Explorez nos gammes exclusives et trouvez l'inspiration pour vos projets."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "FLOORING &amp; TILING"
msgstr "SOL &amp; CARRELAGE"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Fap ceramiche"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Faucets"
msgstr "Robinetterie"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_contactus_section
msgid ""
"Feel free to contact us with any questions, quote requests, or simply to "
"discuss your project."
msgstr ""
"N'hésitez pas à nous contacter pour toute question, demande de devis, ou "
"simplement pour discuter de votre projet."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Floor &amp; Tiles"
msgstr "Sol &amp; Carrelage"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Floor &amp; Wall Covering"
msgstr "Revêtement Sol &amp; Mur"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Floor and Tiles"
msgstr "Sol et Carrelage"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "Flooring and Tiling"
msgstr "Sol et Carrelage"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Full name"
msgstr "Nom complet"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Generate quotation"
msgstr "Générer un devis"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Geotiles"
msgstr ""

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Get my quotation"
msgstr "Telecharger mon devis"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_products_section
msgid "Good Deals"
msgstr "Bonnes Affaires"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Granitifiandre"
msgstr ""

#. module: ceratech_website
#: model:ir.model,name:ceratech_website.model_ir_http
msgid "HTTP Routing"
msgstr "Routage HTTP"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/models/product_template.py:0
#: model:ir.model.fields,field_description:ceratech_website.field_product_product__height
#: model:ir.model.fields,field_description:ceratech_website.field_product_template__height
#, python-format
msgid "Height (m)"
msgstr "Hauteur (m)"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "I want to receive the newsletter. I have read the"
msgstr "Je souhaite recevoir la newsletter. J'ai lu la"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_about_us_section
msgid "Interior design space with a grey sofa"
msgstr "Espace de design intérieur avec un canapé gris"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.product_template_form_view_inherit_surface
msgid "Item Dimensions"
msgstr "Dimensions de l'article"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/models/product_template.py:0
#: model:ir.model.fields,field_description:ceratech_website.field_product_product__items_per_carton
#: model:ir.model.fields,field_description:ceratech_website.field_product_template__items_per_carton
#, python-format
msgid "Items per carton"
msgstr "Articles par carton"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Ivory Coast"
msgstr "Côte d'Ivoire"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Keraben grupo"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_about_us_section
msgid "LEARN MORE"
msgstr "EN SAVOIR PLUS"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "LVT"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Learn more"
msgstr "En savoir plus"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/models/product_template.py:0
#: model:ir.model.fields,field_description:ceratech_website.field_product_product__length
#: model:ir.model.fields,field_description:ceratech_website.field_product_template__length
#, python-format
msgid "Length (m)"
msgstr "Longueur (m)"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Lovex sanitary"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Lv Granito"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Marble Look"
msgstr "Imitation Marbre"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "Mastercard"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "Materials selected for their resistance and ease of maintenance."
msgstr ""
"Des matériaux sélectionnés pour leur résistance et leur facilité "
"d'entretien."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_products_section
msgid "More products"
msgstr "Plus de produits"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Mosaic and Pool Tiles"
msgstr "Mosaïque et Carrelage de Piscine"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/controllers/quote_controller.py:0
#, python-format
msgid "Name, email and phone are required for guests."
msgstr "Le nom, l’e-mail et le téléphone sont obligatoires pour les invités."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Newsletter"
msgstr ""

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/controllers/main.py:0
#, python-format
msgid "No pricelist found for currency %s."
msgstr "Aucune liste de prix trouvée pour la devise %s."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "Number of cartons"
msgstr "Nombre de cartons"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "OUR PARTNERS"
msgstr "NOS PARTENAIRES"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "Orange Money"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "OrangeMoney"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "Our Ceratech Team"
msgstr "Notre équipe Ceratech"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_contactus_section
msgid "Our Contact Information"
msgstr "Nos coordonnées"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "Our Core Values"
msgstr "Nos valeurs fondamentales"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "Our Mission"
msgstr "Notre mission"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "Our Trusted Partners"
msgstr "Nos partenaires de confiance"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_trust_section
msgid ""
"Our commitment is based on quality, personalized advice, and customer "
"satisfaction. With CeraTech, you benefit from reliable support from "
"selection to installation."
msgstr ""
"Notre engagement repose sur la qualité, des conseils personnalisés et la "
"satisfaction client. Avec CeraTech, vous bénéficiez d’un accompagnement "
"fiable, de la sélection à l’installation."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "Our commitments"
msgstr "Nos engagements"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid ""
"Our mission is to provide our customers with the best products at the best "
"prices. We are committed to excellence and constantly strive to innovate and"
" improve our offerings. Customer satisfaction is our top priority."
msgstr ""
"Notre mission est d’offrir à nos clients les meilleurs produits au meilleur "
"prix. Nous sommes engagés dans l’excellence et nous nous efforçons "
"constamment d’innover et d’améliorer nos offres. La satisfaction client est "
"notre priorité absolue."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid ""
"Our subscribers have a head start on new products, special offers, and "
"events. The good deals we announce twice a week are described in detail. "
"Would you like to receive our news as well?"
msgstr ""
"Nos abonnés ont une longueur d’avance sur les nouveaux produits, les offres "
"spéciales et les événements. Les bons plans que nous annonçons deux fois par"
" semaine sont décrits en détail. Souhaitez-vous recevoir nos actualités "
"également ?"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid ""
"Our team of experts is always ready to listen, advise, and support you in "
"carrying out your projects."
msgstr ""
"Notre équipe d’experts est toujours prête à vous écouter, vous conseiller et"
" vous accompagner dans la réalisation de vos projets."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Outdoor Stoneware"
msgstr "Grès cérame extérieur"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.product_template_form_view_inherit_surface
msgid "Packaging"
msgstr "Emballage"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Panaria ceramica"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "PayPal"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "Paypal"
msgstr ""

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Phone number"
msgstr "Numéro de téléphone"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Please enter a valid email address."
msgstr "Veuillez saisir une adresse e-mail valide."

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Please enter your name."
msgstr "Veuillez saisir votre nom."

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid "Please enter your phone number."
msgstr "Veuillez saisir votre numéro de téléphone."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Porcelain Stoneware"
msgstr "Grès Cérame"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "Porcelanosa"
msgstr ""

#. module: ceratech_website
#: model:ir.model,name:ceratech_website.model_product_template
msgid "Product"
msgstr "Produit"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "Product Description"
msgstr "Description du produit"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/xml/product_templates.xml:0
#, python-format
msgid "Product Image"
msgstr "Image du produit"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_conception_3d_section
msgid ""
"Project yourself into your future space with our 3D modeling service. Create"
" your atmosphere, test textures, and visualize the result before ordering."
msgstr ""
"Projetez-vous dans votre futur espace grâce à notre service de modélisation "
"3D. Créez votre ambiance, testez les textures et visualisez le résultat "
"avant de commander."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "QUALITY AND DURABILITY"
msgstr "QUALITÉ ET DURABILITÉ"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "Quality and excellence, hand in hand."
msgstr "Qualité et excellence vont de pair."

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/controllers/quote_controller.py:0
#, python-format
msgid "Quote_%s.pdf"
msgstr "Devis_%s.pdf"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "Ready to start your project?"
msgstr "Prêt(e) à démarrer votre projet ?"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Request a quote"
msgstr "Demander un devis"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Resin Look"
msgstr "Imitation Résine"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "SANITARY &amp; FAUCETS"
msgstr "SANITAIRE &amp; ROBINETTERIE"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "SINKS"
msgstr "LAVABOS"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "SPC"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid "STN grupo"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "Sanitary & Faucets"
msgstr "Sanitaires et robinetterie"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Sanitary Ware &amp; Faucets"
msgstr "Sanitaire &amp; Robinetterie"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "See our collections"
msgstr "Découvrez nos collections"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "See our products"
msgstr "Découvrez nos produits"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Senegal"
msgstr "Sénégal"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Service"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "Sinks"
msgstr "Lavabos"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "Slide 1"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "Slide 2"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "Slide 3"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Stone Look"
msgstr "Imitation Pierre"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Subscribe now"
msgstr "Abonnez-vous maintenant"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Subscription in progress"
msgstr "Abonnement en cours"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "Superior Quality"
msgstr "Qualité supérieure"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "Surface (m²)"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.product_template_form_view_inherit_surface
msgid "Surface Calculation"
msgstr "Calcul de surface"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/models/product_template.py:0
#: model:ir.model.fields,field_description:ceratech_website.field_product_product__surface_per_item
#: model:ir.model.fields,field_description:ceratech_website.field_product_template__surface_per_item
#, python-format
msgid "Surface per item (m²)"
msgstr "Surface par article (m²)"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/models/product_template.py:0
#: model:ir.model.fields,field_description:ceratech_website.field_product_product__is_surfacic
#: model:ir.model.fields,field_description:ceratech_website.field_product_template__is_surfacic
#, python-format
msgid "Surface-based Product?"
msgstr "Produit basé sur la surface ?"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_homepage_carousel
msgid "TILES THAT TRANSFORM YOUR SPACES"
msgstr "DES CARREAUX QUI TRANSFORMENT VOS ESPACES"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Terracotta Look"
msgstr "Imitation Terre cuite"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Terrazzo Look"
msgstr "Imitation Terrazzo"

#. module: ceratech_website
#. odoo-javascript
#: code:addons/ceratech_website/static/src/js/quote_from_cart.js:0
#, python-format
msgid ""
"To generate your quotation, please provide your contact details. The PDF "
"will start downloading immediately."
msgstr ""
"Pour générer votre devis, veuillez fournir vos coordonnées. Le PDF "
"commencera à se télécharger immédiatement."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Toggle navigation"
msgstr "Basculer la navigation"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
msgid "Total Price"
msgstr "Prix total"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "Trust &amp; Transparency"
msgstr "Confiance &amp; transparence"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Vinyl"
msgstr "Vinyle"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Visa"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "WALL COVERING"
msgstr "REVÊTEMENT MURAL"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_about_us_section
msgid "WHO ARE WE?"
msgstr "QUI SOMMES-NOUS ?"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_trust_section
msgid "WHY TRUST US?"
msgstr "POURQUOI NOUS FAIRE CONFIANCE ?"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "WPC"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_categories_section
msgid "Wall Covering"
msgstr "Revêtement mural"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.ceratech_product_page_custom
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Wave"
msgstr "Vague"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.contactus_custom
msgid "We are here to listen."
msgstr "Nous sommes à votre écoute."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid ""
"We are your trusted partner for interior and exterior design solutions. With"
" recognized expertise in tiles, sanitary ware, marble, and waterproofing, we"
" are committed to creating exceptional spaces that meet the unique needs and"
" tastes of our customers."
msgstr ""
"Nous sommes votre partenaire de confiance pour les solutions de design "
"intérieur et extérieur. Forts d’une expertise reconnue dans les carreaux, "
"sanitaires, marbre et étanchéité, nous nous engageons à créer des espaces "
"d’exception qui répondent aux besoins et goûts uniques de nos clients."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid ""
"We believe in honest relationships and fair prices so you can build your "
"dreams without compromise."
msgstr ""
"Nous croyons en des relations honnêtes et des prix justes afin que vous "
"puissiez réaliser vos rêves sans compromis."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid ""
"We collaborate with industry leaders to offer you products that combine "
"innovation, design, and durability. Each partner is rigorously selected for "
"their commitment to quality and excellence."
msgstr ""
"Nous collaborons avec les leaders du secteur pour vous offrir des produits "
"alliant innovation, design et durabilité. Chaque partenaire est "
"rigoureusement sélectionné pour son engagement envers la qualité et "
"l’excellence."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid ""
"We select only the best materials from trusted suppliers to ensure "
"impeccable durability and aesthetics."
msgstr ""
"Nous sélectionnons uniquement les meilleurs matériaux auprès de fournisseurs"
" de confiance pour garantir une durabilité et une esthétique irréprochables."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.about_us_page
msgid "We turn your ideas into reality."
msgstr "Nous concrétisons vos idées."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_partners_section
msgid ""
"We work with the most renowned brands in the sector to guarantee you "
"quality, design, and innovation."
msgstr ""
"Nous travaillons avec les marques les plus renommées du secteur pour vous "
"garantir qualité, design et innovation."

#. module: ceratech_website
#: model:ir.model,name:ceratech_website.model_website
msgid "Website"
msgstr "Site Web"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/controllers/quote_controller.py:0
#, python-format
msgid "Website quotation - %(order)s for %(partner)s"
msgstr "Devis site web - %(order)s pour %(partner)s"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_location_section
msgid "Where to find us?"
msgstr "Où nous trouver ?"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/models/product_template.py:0
#: model:ir.model.fields,field_description:ceratech_website.field_product_product__width
#: model:ir.model.fields,field_description:ceratech_website.field_product_template__width
#, python-format
msgid "Width (m)"
msgstr "Largeur (m)"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Wood Look"
msgstr "Imitation Bois"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Your Language"
msgstr "Votre langue"

#. module: ceratech_website
#. odoo-python
#: code:addons/ceratech_website/controllers/quote_controller.py:0
#, python-format
msgid "Your cart is empty."
msgstr "Votre panier est vide."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "Your email address..."
msgstr "Votre adresse email..."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "alaplana"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "and I am aware that I can unsubscribe at any time and without charge."
msgstr ""
"et je suis conscient(e) que je peux me désabonner à tout moment et sans "
"frais."

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "antica"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "atlas"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "azulejos"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "belite"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
#: model_terms:ir.ui.view,arch_db:ceratech_website.s_ceratech_contactus_section
msgid "<EMAIL>"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "fap"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "geotiles"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "granitifiandre"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "keraben"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "lovex"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "lv"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "panaria"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "porcelanosa"
msgstr ""

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.layout_view_ceratech
msgid "privacy policy and data protection"
msgstr "politique de confidentialité et protection des données"

#. module: ceratech_website
#: model_terms:ir.ui.view,arch_db:ceratech_website.partners_page
msgid "stn"
msgstr ""
