{
    'name': 'Ceratech Website Theme',
    'version': '********.0',
    'category': 'Website/Theme',
    'summary': 'Custom theme for Ceratech website',
    'description': """
        Custom theme for Ceratech website
        =======================================
        
        This module contains Ceratech website theme customizations,
        including a custom country selector, advanced search bar,
        and a modern, responsive user interface.
    """,
    'author': 'Ceratech',
    'website': 'https://www.ceratech.com',
    'depends': ['base', 'crm', 'website', 'website_sale', 'website_sale_wishlist', 'website_mass_mailing'],
    'data': [
        'views/head.xml',
        'views/views.xml',
        'views/about_us_page.xml',
        'views/home.xml',
        'views/product_template_views.xml',
        'views/snippets/home_banner.xml',
        'views/snippets/home_categories.xml',
        'views/snippets/home_products.xml',
        'views/snippets/home_conception_3d.xml',
        'views/snippets/home_partners.xml',
        'views/snippets/home_about_us.xml',
        'views/snippets/home_trust.xml',
        'views/snippets/home_location.xml',
        'views/snippets/contactus_contact.xml',
        'views/partners_page.xml',
        'views/contactus_page.xml',
        'views/shop_views.xml',
        'views/product_page_custom.xml',
        'views/sale_order_report_inherit.xml',
    ],
    'demo': [],
    'assets': {
        'web._assets_primary_variables': [
            ('after', 'website/static/src/scss/primary_variables.scss', 'ceratech_website/static/src/scss/primary_variables.scss'),
        ],
        'web.assets_frontend': [
            'ceratech_website/static/src/scss/ceratech_website.scss',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            
            # XML templates should be loaded before the JS that uses them
            'ceratech_website/static/src/xml/product_templates.xml',

            # JS files
            'ceratech_website/static/src/js/country_selector.js',
            'ceratech_website/static/src/js/quote_from_cart.js',
            'ceratech_website/static/src/js/home_products.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
