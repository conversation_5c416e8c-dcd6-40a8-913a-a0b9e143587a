# Thème Ceratech Website

Ce module contient le thème personnalisé pour le site web de Ceratech, offrant une expérience utilisateur moderne et réactive avec des fonctionnalités avancées comme le sélecteur de pays, la recherche avancée et une interface utilisateur intuitive.

## Fonctionnalités

- **Sélecteur de pays** : Permet aux utilisateurs de sélectionner leur pays et devise (Sénégal, Cameroun, Côte d'Ivoire)
- **Barre de recherche avancée** : Recherche rapide et intuitive des produits
- **Design réactif** : S'adapte à tous les appareils (ordinateurs, tablettes, mobiles)
- **Interface moderne** : Design épuré avec des animations fluides
- **Intégration complète** : Compatible avec tous les modules standards d'Odoo

## Installation

1. Copiez le dossier `ceratech_website` dans votre dossier d'addons personnalisés
2. Activez le mode développeur dans Odoo
3. Allez dans Applications > Mettre à jour la liste des applications
4. Recherchez "Ceratech Website Theme" et installez-le

## Structure du projet

```
ceratech_website/
├── controllers/               # Contrôleurs personnalisés
│   ├── __init__.py
│   └── main.py               # Gestion des routes API pour le sélecteur de pays
├── models/                   # Modèles personnalisés
│   ├── __init__.py
│   └── website.py            # Personnalisations du modèle website
├── static/                   # Fichiers statiques
│   └── src/
│       ├── css/
│       │   ├── ceratech_website.css        # Styles principaux
│       │   └── ceratech_website_editor.css # Styles pour l'éditeur
│       ├── js/
│       │   └── ceratech_website.js         # JavaScript personnalisé
│       └── img/                            # Images du thème
│           └── flags/                      # Drapeaux des pays
├── views/
│   ├── assets.xml            # Déclaration des ressources statiques
│   └── templates.xml         # Templates personnalisés
├── __init__.py
├── __manifest__.py
└── README.md
```

## Configuration

### Sélecteur de pays

Le sélecteur de pays est configuré dans `controllers/main.py` et utilise les fichiers SVG des drapeaux situés dans `static/src/img/flags/`.

Pour ajouter un nouveau pays :

1. Ajoutez le drapeau au format SVG dans le dossier `static/src/img/flags/`
2. Mettez à jour la liste des pays dans `controllers/main.py`
3. Ajoutez les traductions si nécessaire

### Personnalisation des couleurs

Les variables CSS sont définies dans `static/src/css/ceratech_website.css` :

```css
:root {
    --ceratech-primary: #2c75ff;         /* Couleur principale */
    --ceratech-primary-hover: #1a5feb;   /* Couleur principale au survol */
    --ceratech-secondary: #6c757d;       /* Couleur secondaire */
    --ceratech-light: #f8f9fa;           /* Couleur de fond claire */
    --ceratech-dark: #343a40;            /* Couleur de texte foncée */
    --ceratech-border: #dee2e6;          /* Couleur des bordures */
    --ceratech-text: #212529;            /* Couleur de texte principale */
    --ceratech-text-light: #6c757d;      /* Couleur de texte secondaire */
}
```

## Développement

### Prérequis

- Odoo 17.0+
- Node.js et npm (pour la compilation des assets si nécessaire)

### Déploiement

1. Mettez à jour la version dans `__manifest__.py`
2. Testez toutes les fonctionnalités
3. Créez une archive du module
4. Déployez sur l'environnement de production

## Licence

LGPL-3

## Support

Pour toute question ou support, veuillez contacter l'équipe technique de Ceratech.

