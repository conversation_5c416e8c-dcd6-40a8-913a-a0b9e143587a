<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="product_template_form_view_inherit_surface" model="ir.ui.view">
            <field name="name">product.template.form.inherit.surface</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <notebook position="inside">
                    <page string="Surface Calculation" name="surface_calculation">
                        <group>
                            <group>
                                <field name="is_surfacic"/>
                            </group>
                        </group>
                        <group invisible="not is_surfacic">
                            <group string="Item Dimensions">
                                <field name="length"/>
                                <field name="width"/>
                                <field name="height"/>
                            </group>
                            <group string="Packaging">
                                <field name="items_per_carton"/>
                                <field name="surface_per_item" readonly="1"/>
                            </group>
                        </group>
                    </page>
                </notebook>
            </field>
        </record>

    </data>
</odoo>
