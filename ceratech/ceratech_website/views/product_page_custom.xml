<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="ceratech_product_page_custom" inherit_id="website_sale.product" name="Ceratech Product Page Customizations">

        <!-- I. Right Column (Product Info) Modifications -->

        <!-- 1. Style the product title -->
        <xpath expr="//h1[@t-field='product.name']" position="attributes">
            <attribute name="class" add="display-5 fw-bold" separator=" "/>
        </xpath>

        <!-- 3. Add delivery info -->
        <!-- <xpath expr="//div[@id='add_to_cart_wrap']" position="before">
            <div class="d-flex align-items-center my-3 ms-5">
                <i class="fa fa-truck me-2"/>
                <small>DELIVERY</small>
            </div>
        </xpath> -->

        <!-- 4. Add new fields 'Surface' and 'Total Price' -->
        <xpath expr="//div[@id='add_to_cart_wrap']" position="before">
            <div t-if="product.is_surfacic" class="calculator-section mb-3">
                <!-- Script and hidden data -->
                <script type="text/javascript" src="/ceratech_website/static/src/js/surface_calculator.js"/>
                <span id="surface_per_item" class="d-none" t-esc="product.surface_per_item"/>
                <span id="items_per_carton" class="d-none" t-esc="product.items_per_carton"/>
                <span id="price_per_carton" class="d-none" t-esc="product.list_price"/>

                <!-- Visible fields -->
                <div class="row g-2 align-items-end">
                    <div class="col-6">
                        <label for="surface_input" class="form-label small">Surface (m²)</label>
                        <input type="number" class="form-control" id="surface_input" value="" min="0"/>
                    </div>
                    <div class="col-6">
                        <label class="form-label small">Total Price</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="total_price_result" value="0.00" readonly="readonly"/>
                            <span class="input-group-text" t-esc="pricelist.currency_id.symbol"/>
                        </div>
                    </div>
                </div>
                <div class="row g-2 mt-2 d-none"> <!-- To display for debugging calculations (remove 'd-none') -->
                    <div class="col-12">
                        <p class="mb-1"><i class="fa fa-th-large me-2"/> Required pieces: <strong id="pieces_result">0</strong></p>
                        <p class="mb-0"><i class="fa fa-archive me-2"/> Required cartons: <strong id="cartons_result">0</strong></p>
                    </div>
                </div>
            </div>
        </xpath>

        <!-- II. Left Column (Image Gallery) Modifications -->

        <!-- 8. Add 3D Render button and payment methods image -->
        <xpath expr="//div[@id='product_details']/preceding-sibling::div" position="inside">
            <div class="d-flex flex-wrap gap-2 mt-3 align-items-center">
                <a href="#" class="btn btn-primary d-flex align-items-center">3D Render</a>
                <div class="p-2 flex-grow-1">
                    <div class="d-flex flex-wrap justify-content-center align-items-center gap-2">
                        <img src="/ceratech_website/static/src/img/payment/mastercard.jpg" alt="Mastercard" class="payment-icon"/>
                        <img src="/ceratech_website/static/src/img/payment/orangemoney.jpg" alt="Orange Money" class="payment-icon"/>
                        <img src="/ceratech_website/static/src/img/payment/paypal.jpg" alt="Paypal" class="payment-icon"/>
                        <img src="/ceratech_website/static/src/img/payment/visa.jpg" alt="Visa" class="payment-icon"/>
                        <img src="/ceratech_website/static/src/img/payment/wave.jpg" alt="Wave" class="payment-icon"/>
                    </div>
                </div>
            </div>
        </xpath>

        <!-- III. Bottom Section (Description & Tech Sheet) Modifications -->

        <!-- 9. Add new Description and Tech Sheet sections -->
        <xpath expr="//div[@id='product_details']" position="after">
            <div class="container">
                <!-- Description -->
                <h4 class="text-center mt-5 mb-3">Product Description</h4>
                <div class="description-box mb-5">
                    <t t-esc="product.description_sale"/>
                </div>
            </div>
        </xpath>

        <!-- IV. Align Cart & Wishlist buttons on the same line -->
        <xpath expr="//div[@id='add_to_cart_wrap']" position="attributes">
            <attribute name="class">d-inline-flex align-items-center mb-2 me-2</attribute>
        </xpath>
        <xpath expr="//div[@id='product_option_block']" position="attributes">
            <attribute name="class">d-inline-flex align-items-center gap-2</attribute>
        </xpath>

        <!-- V. Add the WhatsApp button in a separate div right after product_option_block -->
        <xpath expr="//div[@id='product_option_block']" position="after">
            <div class="d-inline-flex align-items-center ms-3">
                <a href="https://wa.me/" class="btn btn-success rounded-circle p-0 d-flex align-items-center justify-content-center" style="width:48px;height:48px;">
                    <i class="fa fa-whatsapp fa-lg text-white"/>
                </a>
            </div>
        </xpath>

        <!-- VI. Add the number of cartons label on the quantity selector if it is a surfacic product -->
        <xpath expr="//div[@id='o_wsale_cta_wrapper']" position="before">
            <div t-if="product.is_surfacic" class="mt-1">
                <hr/>
                <label class="form-label small">Number of cartons</label>
            </div>
        </xpath>

    </template>

</odoo>