<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="products_item_attributes" inherit_id="website_sale.products_item" name="Show Product Attributes">
        <xpath expr="//div[hasclass('o_wsale_product_information')]" position="inside">
            <!-- Display product variant attributes in styled boxes -->
            <div class="product_attributes_custom d-flex flex-wrap align-items-center mt-2">
                <t t-foreach="product.product_variant_id.product_template_attribute_value_ids" t-as="ptav">
                    <span class="border small text-muted me-1 mb-1 py-1 px-2">
                        <t t-esc="ptav.name"/>
                    </span>
                </t>
            </div>
        </xpath>
    </template>

</odoo>
