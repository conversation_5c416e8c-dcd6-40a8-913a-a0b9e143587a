# -*- coding: utf-8 -*-
from odoo import models


class IrHttp(models.AbstractModel):
    _inherit = 'ir.http'

    @classmethod
    def _get_translation_frontend_modules_name(cls):
        """ 
        This method is inherited to force the loading of the module's translations
        on the frontend. Odoo's automatic detection can sometimes miss a module,
        preventing its JavaScript translations from being loaded.
        By explicitly adding 'ceratech_website' to the list of modules, we ensure
        that its translations are always included in the /website/translations payload.
        """
        mods = super(IrHttp, cls)._get_translation_frontend_modules_name()
        return mods + ['ceratech_website']
