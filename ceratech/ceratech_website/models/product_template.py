from odoo import models, fields, api, _
import math
import re

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Main control field
    is_surfacic = fields.Boolean(string=_("Surface-based Product?"))

    # Dimensions (visible if is_surfacic is true)
    length = fields.Float(string=_("Length (m)"))
    width = fields.Float(string=_("Width (m)"))
    height = fields.Float(string=_("Height (m)"))

    # Data for calculation
    items_per_carton = fields.Integer(
        string=_("Items per carton"), 
        compute='_compute_items_per_carton',
        store=True,
        readonly=False, # Allow manual override if needed
        default=1
    )

    @api.depends('packaging_ids')
    def _compute_items_per_carton(self):
        for product in self:
            # Default value
            calculated_value = 1
            # Find the first packaging with a parsable quantity in its type name
            for packaging in product.packaging_ids:
                if packaging.package_type_id and packaging.package_type_id.name:
                    # Extract all digits from the string
                    match = re.search(r'\d+', packaging.package_type_id.name)
                    if match:
                        try:
                            calculated_value = int(match.group(0))
                            # We found a valid value, stop searching
                            break
                        except (ValueError, TypeError):
                            continue
            product.items_per_carton = calculated_value

    # Computed fields (will not be stored in the database, but computed on the fly)
    # Note: These fields only make sense in the context of a sale (sale.order.line)
    # where the customer's desired surface area is known. We leave them here
    # for structure, but the calculation logic will be mainly on the sales/website side.

    @api.depends('length', 'width')
    def _compute_surface_per_item(self):
        for product in self:
            if product.is_surfacic and product.length > 0 and product.width > 0:
                product.surface_per_item = product.length * product.width
            else:
                product.surface_per_item = 0

    surface_per_item = fields.Float(string=_("Surface per item (m²)"), compute='_compute_surface_per_item', store=True, help=_("Calculated surface for a single item."))

    # The 'pieces' and 'cartons' fields depend on a surface area that is not on the product itself.
    # They will therefore be calculated and managed on the website side and/or in the sales order lines.
