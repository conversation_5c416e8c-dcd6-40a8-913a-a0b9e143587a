from odoo import models, fields, api


class StockMove(models.Model):
    _inherit = "stock.move"

    @api.depends("product_id")
    def _compute_description_picking(self):
        """Override to ensure description_picking uses product name with variants but without internal reference"""
        for move in self:
            if move.product_id:
                # Start with product name (without internal reference)
                name = move.product_id.name

                # Add variant information if it exists
                if move.product_id.product_template_attribute_value_ids:
                    variant_name = (
                        move.product_id.product_template_attribute_value_ids._get_combination_name()
                    )
                    if variant_name:
                        name += f" ({variant_name})"

                move.description_picking = name
            else:
                move.description_picking = ""


class StockMoveLine(models.Model):
    _inherit = "stock.move.line"

    def _get_aggregated_product_quantities(self, strict=False):
        """Override to ensure aggregated lines use product name without internal reference"""
        aggregated_move_lines = super()._get_aggregated_product_quantities(
            strict=strict
        )

        # Update the 'name' field in aggregated lines to use product name only
        for key in aggregated_move_lines:
            if "name" in aggregated_move_lines[key]:
                # Extract product from the move line
                move_line = self.filtered(
                    lambda ml: ml.product_id.id == key[0]
                    and (
                        not strict
                        or (
                            ml.lot_id.id == key[1]
                            and ml.result_package_id.id == key[2]
                            and ml.owner_id.id == key[3]
                        )
                    )
                )[:1]

                if move_line and move_line.product_id:
                    # Start with product name (without internal reference)
                    name = move_line.product_id.name

                    # Add variant information if it exists
                    if move_line.product_id.product_template_attribute_value_ids:
                        variant_name = (
                            move_line.product_id.product_template_attribute_value_ids._get_combination_name()
                        )
                        if variant_name:
                            name += f" ({variant_name})"

                    aggregated_move_lines[key]["name"] = name

        return aggregated_move_lines
