from odoo import models, fields, api


class StockMove(models.Model):
    _inherit = "stock.move"

    @api.depends("product_id")
    def _compute_description_picking(self):
        """
        MODIFIED: This method now allows the default behavior to show internal references in stock reports.

        We no longer override this method to hide internal references in delivery slips.
        The default Odoo behavior will be used, which includes internal references in stock reports.

        This ensures that:
        - Invoice reports: Hide internal references (handled by account_move_line.py)
        - Sale reports: Hide internal references (handled by sale_order_line.py)
        - Stock reports: Show internal references (default Odoo behavior restored)
        """
        # Call the parent method to restore default behavior
        super()._compute_description_picking()


class StockMoveLine(models.Model):
    _inherit = "stock.move.line"

    def _get_aggregated_product_quantities(self, strict=False):
        """
        MODIFIED: This method now allows the default behavior to show internal references in aggregated stock lines.

        We no longer override the 'name' field in aggregated lines to hide internal references.
        The default Odoo behavior will be used, which includes internal references in stock reports.
        """
        # Call the parent method to restore default behavior with internal references
        return super()._get_aggregated_product_quantities(strict=strict)
