from odoo import models, fields, api, _


class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    @api.depends("product_id")
    def _compute_name(self):
        """Override to display only product name without internal reference in invoice reports"""
        for line in self.filtered(lambda l: l.move_id.inalterable_hash is False):
            if line.display_type == "payment_term":
                term_lines = (
                    line.move_id.line_ids.filtered(
                        lambda l: l.display_type == "payment_term"
                    )
                    | line
                )
                name = line.move_id.payment_reference or ""
                if len(term_lines) > 1:
                    index = term_lines._ids.index(line.id) + 1
                    name = _("%s installment #%s", name, index).lstrip()
                line.name = name
            if not line.product_id or line.display_type in (
                "line_section",
                "line_note",
            ):
                continue
            if line.partner_id.lang:
                product = line.product_id.with_context(lang=line.partner_id.lang)
            else:
                product = line.product_id

            values = []
            # Skip partner_ref to avoid including internal reference
            # if product.partner_ref:
            #     values.append(product.partner_ref)

            if line.journal_id.type == "sale":
                if product.description_sale:
                    values.append(product.description_sale)
                else:
                    # Use product name without default_code
                    values.append(product.name)
            elif line.journal_id.type == "purchase":
                if product.description_purchase:
                    values.append(product.description_purchase)
                else:
                    # Use product name without default_code
                    values.append(product.name)

            line.name = "\n".join(values) if values else product.name
