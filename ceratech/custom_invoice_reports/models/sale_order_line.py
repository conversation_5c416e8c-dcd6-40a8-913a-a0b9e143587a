from odoo import models, fields, api, _


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    def _get_sale_order_line_multiline_description_sale(self):
        """Override to display product name with variants but without internal reference"""
        self.ensure_one()

        if not self.product_id:
            return ""

        # Start with product name (without internal reference)
        name = self.product_id.name

        # Add variant information if it exists
        variant_description = self._get_sale_order_line_multiline_description_variants()
        if variant_description:
            name += variant_description

        # Add description_sale if available (on new line)
        if self.product_id.description_sale:
            name += "\n" + self.product_id.description_sale

        return name

    @api.depends("product_id")
    def _compute_name(self):
        """Override to ensure consistent naming without internal reference"""
        for line in self:
            if not line.product_id:
                continue
            lang = line.order_id._get_lang()
            if lang != self.env.lang:
                line = line.with_context(lang=lang)
            name = line._get_sale_order_line_multiline_description_sale()
            if line.is_downpayment and not line.display_type:
                context = {"lang": lang}
                dp_state = line._get_downpayment_state()
                if dp_state == "draft":
                    name = _("%(line_description)s (Draft)", line_description=name)
                elif dp_state == "cancel":
                    name = _("%(line_description)s (Canceled)", line_description=name)
                else:
                    invoice = line._get_invoice_lines().move_id
                    if (
                        len(invoice) == 1
                        and invoice.payment_reference
                        and invoice.invoice_date
                    ):
                        name = _(
                            "%(line_description)s (ref: %(reference)s on %(date)s)",
                            line_description=name,
                            reference=invoice.payment_reference,
                            date=invoice.invoice_date,
                        )
            line.name = name
