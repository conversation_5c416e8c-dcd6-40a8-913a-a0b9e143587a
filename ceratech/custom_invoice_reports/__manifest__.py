{
    "name": "Custom Reports - Selective Internal Reference Display",
    "version": "1.1",
    "category": "Reporting",
    "summary": "Selective internal reference display: hidden in customer documents, visible in internal documents",
    "description": """
        This module customizes reports with selective internal reference (default_code) display:

        Customer-facing Documents (HIDDEN internal reference):
        - Invoice Reports: Standard Invoice (Facture), Price Note (Note de prix)
        - Sale Reports: Quotations, Sale Orders

        Internal Documents (VISIBLE internal reference):
        - Stock Reports: Delivery Slips, Transfer Documents

        Features:
        - Hides internal reference (default_code) from customer-facing documents (invoices, quotations)
        - Shows internal reference (default_code) in internal documents (delivery slips, stock reports)
        - Maintains existing custom formatting for Ceratech
        - Provides clear separation between customer and internal document formatting
    """,
    "depends": ["base", "account", "sale", "stock"],
    "data": [
        # "views/account_move_views.xml",
        "views/report_invoice_templates.xml",
        "views/report_sale_templates.xml",
        "views/report_stock_templates.xml",
    ],
    "installable": True,
    "application": False,
    "auto_install": False,
}
