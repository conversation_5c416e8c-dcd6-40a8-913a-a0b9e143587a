<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Override Sale Order (Quotation) template to show only product name without internal reference -->
    <template id="custom_report_saleorder" inherit_id="sale.report_saleorder_document">

        <!-- Override product description in sale order lines to show only product name without internal reference but keep variants -->
        <xpath expr="//td[@name='td_name']/span[@t-field='line.name']" position="replace">
            <span>
                <t t-if="line.product_id">
                    <!-- Show product name without internal reference -->
                    <span t-field="line.product_id.name" t-options="{'widget': 'text'}"/>
                    <!-- Add variant information if it exists -->
                    <t t-if="line.product_id.product_template_attribute_value_ids">
                        <t t-set="variant_name" t-value="line.product_id.product_template_attribute_value_ids._get_combination_name()"/>
                        <t t-if="variant_name">
                            <span t-esc="' (' + variant_name + ')'"/>
                        </t>
                    </t>
                    <!-- Add description_sale if available -->
                    <t t-if="line.product_id.description_sale">
                        <br/>
                        <span t-field="line.product_id.description_sale" t-options="{'widget': 'text'}"/>
                    </t>
                </t>
                <t t-else="">
                    <span t-field="line.name" t-options="{'widget': 'text'}"/>
                </t>
            </span>
        </xpath>

        <!-- Also handle section and note lines to ensure consistency -->
        <xpath expr="//td[@name='td_section_line']/span[@t-field='line.name']" position="replace">
            <span t-field="line.name" t-options="{'widget': 'text'}"/>
        </xpath>

        <xpath expr="//td[@name='td_note_line']/span[@t-field='line.name']" position="replace">
            <span t-field="line.name" t-options="{'widget': 'text'}"/>
        </xpath>

    </template>
</odoo>
