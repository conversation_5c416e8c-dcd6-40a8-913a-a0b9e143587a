<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Override Stock Picking (Delivery Slip) template to show only product name without internal reference -->
    <template id="custom_report_delivery" inherit_id="stock.report_delivery_document">

        <!-- Override product display in main delivery table -->
        <xpath expr="//span[@t-field='move.product_id']" position="replace">
            <span>
                <!-- Show product name without internal reference -->
                <span t-field="move.product_id.name"/>
                <!-- Add variant information if it exists -->
                <t t-if="move.product_id.product_template_attribute_value_ids">
                    <t t-set="variant_name" t-value="move.product_id.product_template_attribute_value_ids._get_combination_name()"/>
                    <t t-if="variant_name">
                        <span t-esc="' (' + variant_name + ')'"/>
                    </t>
                </t>
            </span>
        </xpath>

        <!-- Override product display in backorder table -->
        <xpath expr="//span[@t-field='bo_line.product_id']" position="replace">
            <span>
                <!-- Show product name without internal reference -->
                <span t-field="bo_line.product_id.name"/>
                <!-- Add variant information if it exists -->
                <t t-if="bo_line.product_id.product_template_attribute_value_ids">
                    <t t-set="variant_name" t-value="bo_line.product_id.product_template_attribute_value_ids._get_combination_name()"/>
                    <t t-if="variant_name">
                        <span t-esc="' (' + variant_name + ')'"/>
                    </t>
                </t>
            </span>
        </xpath>

    </template>

    <!-- Override the serial number template -->
    <template id="custom_stock_report_delivery_has_serial_move_line" inherit_id="stock.stock_report_delivery_has_serial_move_line">

        <!-- Override product display in serial number lines -->
        <xpath expr="//span[@t-field='move_line.product_id']" position="replace">
            <span>
                <!-- Show product name without internal reference -->
                <span t-field="move_line.product_id.name"/>
                <!-- Add variant information if it exists -->
                <t t-if="move_line.product_id.product_template_attribute_value_ids">
                    <t t-set="variant_name" t-value="move_line.product_id.product_template_attribute_value_ids._get_combination_name()"/>
                    <t t-if="variant_name">
                        <span t-esc="' (' + variant_name + ')'"/>
                    </t>
                </t>
            </span>
        </xpath>

    </template>

    <!-- Override the aggregated lines template -->
    <template id="custom_stock_report_delivery_aggregated_move_lines" inherit_id="stock.stock_report_delivery_aggregated_move_lines">

        <!-- The aggregated lines use a different structure, we need to override the name display -->
        <!-- This is more complex as it uses computed aggregated_lines data -->
        <!-- We'll need to handle this in the Python model override -->

    </template>
</odoo>
