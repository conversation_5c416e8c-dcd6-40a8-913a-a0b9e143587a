# Custom Reports - Selective Internal Reference Display - Ceratech

## Overview

This module customizes business reports for Ceratech with selective internal reference (default_code) display:

- **Hides** internal references in customer-facing documents (invoices, quotations)
- **Shows** internal references in internal documents (delivery slips, stock reports)

## Features

1. **Invoice Reports Customization**:

   - Displays only the product name or description_sale field
   - Removes internal reference (default_code) from invoice line descriptions
   - Applies to both standard invoices (Facture) and price notes (Note de prix)

2. **Sale Order/Quotation Reports Customization**:

   - Removes internal reference from quotation and sale order line descriptions
   - Uses product name or description_sale field only
   - Maintains all other quotation formatting

3. **Stock/Delivery Slip Reports Customization**:

   - **SHOWS** internal reference in delivery slip product descriptions (restored default behavior)
   - Applies to all stock picking documents
   - Handles both individual and aggregated product lines
   - Internal references are visible for warehouse/logistics operations

4. **Existing Features Maintained**:
   - Custom header formatting for Facture and Note de prix
   - Company-specific styling and signatures
   - Custom price unit formatting
   - All existing Ceratech customizations preserved

## Technical Implementation

### Method 1: QWeb Template Override (Recommended)

The module overrides the QWeb template for invoice line descriptions using XPath expressions:

```xml
<xpath expr="//td[@name='account_invoice_line_name']/span[@t-field='line.name']" position="replace">
    <span t-if="line.name">
        <t t-if="line.product_id and line.product_id.description_sale">
            <span t-field="line.product_id.description_sale" t-options="{'widget': 'text'}"/>
        </t>
        <t t-elif="line.product_id">
            <span t-field="line.product_id.name" t-options="{'widget': 'text'}"/>
        </t>
        <t t-else="">
            <span t-field="line.name" t-options="{'widget': 'text'}"/>
        </t>
    </span>
</xpath>
```

### Python Model Overrides

The module includes comprehensive Python model overrides for consistent behavior across all document types:

- **`account.move.line`**: Modifies `_compute_name` to exclude `partner_ref` field
- **`sale.order.line`**: Overrides `_get_sale_order_line_multiline_description_sale` and `_compute_name`
- **`stock.move`**: Modifies `_compute_description_picking` to use product name only
- **`stock.move.line`**: Updates `_get_aggregated_product_quantities` for delivery reports

## Installation

1. The module is located in `ceratech/custom_invoice_reports/`
2. Install through Odoo Apps interface or using the command line
3. Update the module if it was previously installed

## Usage

Once installed, business reports will display product descriptions with selective internal reference visibility:

**Customer-facing documents (invoices, quotations):**

- **Before**: `[REF001] Product Name (Variant)`
- **After**: `Product Name (Variant)`

**Internal documents (delivery slips, stock reports):**

- **Before**: `[REF001] Product Name (Variant)`
- **After**: `[REF001] Product Name (Variant)` (unchanged - shows internal reference)

The changes apply to:

**Invoice Reports:**

- Standard invoices (Facture)
- Price notes (Note de prix)
- All invoice line items with products

**Sale Reports:**

- Quotations
- Sale orders
- All sale order line items with products

**Stock Reports:**

- Delivery slips (shows internal references)
- Transfer documents (shows internal references)
- All stock move lines with products (shows internal references)

## Dependencies

- `base`
- `account`
- `sale`
- `stock`

## Compatibility

- Odoo 17 Enterprise
- Tested with Ceratech SARL configuration

## Notes

- The module preserves existing custom formatting and styling
- Only affects the display in reports, not the underlying data
- Product internal references are still available in the product form and other views
